"""
块管理器模块 - 用于管理KV缓存的内存块分配和缓存机制

该模块实现了基于块的内存管理系统，用于高效管理大语言模型推理过程中的KV缓存。
主要特性：
1. 块级内存分配和回收
2. 基于哈希的缓存机制，支持前缀缓存
3. 引用计数管理，支持多序列共享相同的缓存块
4. 动态块分配，支持序列的增量生成
"""

from collections import deque
import xxhash
import numpy as np

from nanovllm.engine.sequence import Sequence


class Block:
    """
    内存块类 - 表示KV缓存中的一个固定大小的内存块

    每个块包含：
    - 唯一的块ID
    - 引用计数（支持多序列共享）
    - 哈希值（用于缓存查找）
    - token_ids（该块包含的token序列）
    """

    def __init__(self, block_id):
        """
        初始化内存块

        Args:
            block_id: 块的唯一标识符
        """
        self.block_id = block_id      # 块的唯一ID
        self.ref_count = 0            # 引用计数，表示有多少个序列在使用这个块
        self.hash = -1                # 块内容的哈希值，-1表示未计算或无效
        self.token_ids = []           # 该块包含的token ID列表

    def update(self, hash: int, token_ids: list[int]):
        """
        更新块的内容和哈希值

        Args:
            hash: 新的哈希值
            token_ids: 新的token ID列表
        """
        self.hash = hash
        self.token_ids = token_ids

    def reset(self):
        """
        重置块状态，准备重新使用
        将引用计数设为1，清空哈希值和token_ids
        """
        self.ref_count = 1
        self.hash = -1
        self.token_ids = []


class BlockManager:
    """
    块管理器类 - 负责管理所有内存块的分配、回收和缓存

    该类实现了一个高效的内存管理系统，支持：
    1. 块的分配和回收
    2. 基于哈希的缓存机制，避免重复计算
    3. 前缀缓存，提高缓存命中率
    4. 引用计数管理，支持多序列共享缓存
    """

    def __init__(self, num_blocks: int, block_size: int):
        """
        初始化块管理器

        Args:
            num_blocks: 总的内存块数量
            block_size: 每个块的大小（包含的token数量）
        """
        assert num_blocks > 0
        self.block_size = block_size                                          # 每个块的大小
        self.blocks: list[Block] = [Block(i) for i in range(num_blocks)]     # 所有内存块的列表
        self.hash_to_block_id: dict[int, int] = dict()                       # 哈希值到块ID的映射，用于缓存查找
        self.free_block_ids: deque[int] = deque(range(num_blocks))           # 空闲块ID队列
        self.used_block_ids: set[int] = set()                                # 已使用块ID集合

    @classmethod
    def compute_hash(cls, token_ids: list[int], prefix: int = -1):
        """
        计算token序列的哈希值，支持前缀哈希

        前缀哈希机制：通过将前一个块的哈希值作为前缀，可以实现增量哈希计算，
        这样相同前缀的不同序列可以共享缓存，提高缓存效率。

        Args:
            token_ids: 要计算哈希的token ID列表
            prefix: 前缀哈希值，-1表示没有前缀

        Returns:
            计算得到的64位哈希值
        """
        h = xxhash.xxh64()                                    # 使用xxhash算法，速度快且冲突率低
        if prefix != -1:
            h.update(prefix.to_bytes(8, "little"))           # 如果有前缀，先更新前缀哈希
        h.update(np.array(token_ids).tobytes())              # 更新token_ids的字节表示
        return h.intdigest()                                  # 返回整数形式的哈希值

    def _allocate_block(self, block_id: int) -> Block:
        """
        分配一个内存块（私有方法）

        Args:
            block_id: 要分配的块ID

        Returns:
            分配的Block对象
        """
        block = self.blocks[block_id]
        assert block.ref_count == 0                          # 确保块当前未被使用
        block.reset()                                         # 重置块状态
        self.free_block_ids.remove(block_id)                 # 从空闲队列中移除
        self.used_block_ids.add(block_id)                    # 添加到已使用集合
        return self.blocks[block_id]

    def _deallocate_block(self, block_id: int) -> Block:
        """
        回收一个内存块（私有方法）

        Args:
            block_id: 要回收的块ID
        """
        assert self.blocks[block_id].ref_count == 0          # 确保块的引用计数为0
        self.used_block_ids.remove(block_id)                 # 从已使用集合中移除
        self.free_block_ids.append(block_id)                 # 添加回空闲队列

    def can_allocate(self, seq: Sequence) -> bool:
        """
        检查是否有足够的空闲块来分配给指定序列

        Args:
            seq: 要检查的序列

        Returns:
            如果有足够空闲块返回True，否则返回False
        """
        return len(self.free_block_ids) >= seq.num_blocks

    def allocate(self, seq: Sequence):
        """
        为序列分配内存块

        这是块管理器的核心方法，实现了智能的缓存机制：
        1. 对于每个块，首先计算哈希值
        2. 检查是否存在相同哈希的缓存块
        3. 如果缓存命中且内容匹配，则复用缓存块
        4. 如果缓存未命中，则分配新块
        5. 更新序列的缓存统计信息

        Args:
            seq: 要分配内存块的序列
        """
        assert not seq.block_table                           # 确保序列还没有分配过块
        h = -1                                               # 前缀哈希值，初始为-1
        cache_miss = False                                   # 缓存未命中标志

        for i in range(seq.num_blocks):                      # 遍历序列需要的所有块
            token_ids = seq.block(i)                         # 获取第i个块的token_ids

            # 只有当块是满的（大小等于block_size）时才计算哈希，否则设为-1
            h = self.compute_hash(token_ids, h) if len(token_ids) == self.block_size else -1

            # 尝试从缓存中查找相同哈希的块
            block_id = self.hash_to_block_id.get(h, -1)

            # 检查缓存是否命中：哈希存在且内容完全匹配
            if block_id == -1 or self.blocks[block_id].token_ids != token_ids:
                cache_miss = True                            # 标记为缓存未命中

            if cache_miss:
                # 缓存未命中：分配新的空闲块
                block_id = self.free_block_ids[0]           # 获取第一个空闲块ID
                block = self._allocate_block(block_id)      # 分配该块
            else:
                # 缓存命中：复用现有块
                seq.num_cached_tokens += self.block_size    # 增加缓存token计数
                if block_id in self.used_block_ids:
                    # 块已在使用中，增加引用计数
                    block = self.blocks[block_id]
                    block.ref_count += 1
                else:
                    # 块未在使用中，重新分配
                    block = self._allocate_block(block_id)

            # 如果哈希有效，更新块内容和哈希映射
            if h != -1:
                block.update(h, token_ids)                  # 更新块的哈希和内容
                self.hash_to_block_id[h] = block_id         # 更新哈希到块ID的映射

            seq.block_table.append(block_id)                # 将块ID添加到序列的块表中

    def deallocate(self, seq: Sequence):
        """
        回收序列占用的所有内存块

        使用引用计数机制安全地回收内存：
        1. 遍历序列的所有块（逆序，从最后一个开始）
        2. 减少每个块的引用计数
        3. 当引用计数降为0时，回收该块
        4. 清空序列的缓存信息

        Args:
            seq: 要回收内存块的序列
        """
        for block_id in reversed(seq.block_table):           # 逆序遍历块表
            block = self.blocks[block_id]
            block.ref_count -= 1                             # 减少引用计数
            if block.ref_count == 0:                         # 如果没有其他序列引用该块
                self._deallocate_block(block_id)             # 回收该块
        seq.num_cached_tokens = 0                            # 重置缓存token计数
        seq.block_table.clear()                              # 清空块表

    def can_append(self, seq: Sequence) -> bool:
        """
        检查是否可以为序列追加新token

        当序列长度模block_size等于1时，意味着最后一个块已满，
        需要分配新块来容纳新的token。

        Args:
            seq: 要检查的序列

        Returns:
            如果可以追加返回True，否则返回False
        """
        return len(self.free_block_ids) >= (len(seq) % self.block_size == 1)

    def may_append(self, seq: Sequence):
        """
        处理序列追加token时的块管理

        该方法处理三种情况：
        1. 序列长度模block_size等于1：最后一个块已满，需要分配新块
        2. 序列长度模block_size等于0：最后一个块刚好填满，需要计算哈希并缓存
        3. 其他情况：最后一个块未满，无需特殊处理

        Args:
            seq: 要处理的序列
        """
        block_table = seq.block_table
        last_block = self.blocks[block_table[-1]]            # 获取最后一个块

        if len(seq) % self.block_size == 1:
            # 情况1：最后一个块已满（哈希已计算），需要分配新块
            assert last_block.hash != -1                    # 确保最后一个块的哈希已计算
            block_id = self.free_block_ids[0]               # 获取新的空闲块
            self._allocate_block(block_id)                  # 分配新块
            block_table.append(block_id)                    # 添加到块表

        elif len(seq) % self.block_size == 0:
            # 情况2：最后一个块刚好填满，需要计算哈希并更新缓存
            assert last_block.hash == -1                    # 确保最后一个块的哈希未计算
            token_ids = seq.block(seq.num_blocks-1)         # 获取最后一个块的token_ids

            # 获取前缀哈希：如果有前一个块，使用其哈希；否则为-1
            prefix = self.blocks[block_table[-2]].hash if len(block_table) > 1 else -1

            h = self.compute_hash(token_ids, prefix)        # 计算当前块的哈希
            last_block.update(h, token_ids)                 # 更新块的哈希和内容
            self.hash_to_block_id[h] = last_block.block_id  # 更新哈希映射

        else:
            # 情况3：最后一个块未满，哈希应该为-1（未计算状态）
            assert last_block.hash == -1
